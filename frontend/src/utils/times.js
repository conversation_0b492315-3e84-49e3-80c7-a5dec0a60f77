import moment from 'moment';
import 'moment/locale/ru';

const TIME_FORMAT = process.env.REACT_APP_TIMEFORMAT || 'timestamp';

export const getFormatTime = (time, timezone, format = 'DD.MM.YYYY') => {
  let timeTz = unixToMoment(time);

  if (timezone) {
    timeTz = unixToMoment(time).tz(timezone);
  }
  return `${timeTz.format(format)}`;
};

export const unixToMoment = (timestamp) => {
  if (TIME_FORMAT === 'timestamp') {
    return moment.unix(timestamp);
  } else if (TIME_FORMAT === 'iso') {
    return moment.utc(timestamp);
  }

  return moment.unix(timestamp);
};
