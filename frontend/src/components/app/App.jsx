// import styles from './App.module.scss';
import { Route, Router, Switch } from 'react-router-dom';
import history from '../../history';
import MainScreen from '../../pages/main/MainScreen';
import { cityRoutes } from '../../const/routes';
import { connect } from 'react-redux';

function App() {
  return (
    <Router history={history}>
      <Switch>
        { cityRoutes.map((route) => (
          <Route exact path={`${route.path}`} key={route.id}>
            <MainScreen cityIdScreen={route.cityId} />
          </Route>
        )) }
      </Switch>
    </Router>
  );
}

export { App }
export default connect()(App);
