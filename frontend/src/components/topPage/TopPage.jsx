import styles from './TopPage.module.scss';
import { ReactComponent as LogoLiga } from '../../images/svg/main-logo.svg';
import raceLigaPng1x from '../../images/<EMAIL>';
import raceLigaPng2x from '../../images/<EMAIL>';
import raceLigaWebp1x from '../../images/webp/<EMAIL>';
import raceLigaWebp2x from '../../images/webp/<EMAIL>';
import raceLigaAvif1x from '../../images/avif/<EMAIL>';
import raceLigaAvif2x from '../../images/avif/<EMAIL>';

import cloud2 from '../../images/<EMAIL>';
import cloud3 from '../../images/<EMAIL>';
import leftHand from '../../images/<EMAIL>';
import rightHand from '../../images/<EMAIL>';
import legs from '../../images/<EMAIL>';
import land from '../../images/<EMAIL>';
import { useEffect, useState } from 'react';
import CitySelect from '../citySelect/CitySelect';
import '../citySelect/CitySelect.scss';

function TopPage() {
  const [isAnimTopPage, setAnimTopPage] = useState(true);

  useEffect(() => {
    function handleResize() {
      if (window.innerWidth < 991) setAnimTopPage(false);
      if (window.innerWidth > 991) setAnimTopPage(true);
    }

    window.addEventListener("resize", handleResize);

    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className={styles.wrap}>
      <section className={`${styles.topPage} ${isAnimTopPage ? styles.topPageAnimate : ``}`}>
        <div className={styles.landWrap}>
          <img className={styles.land} src={land} alt=""/>
        </div>
        <img className={`${styles.cloud} ${styles.cloud2}`} src={cloud2} alt=""/>
        <img className={`${styles.cloud} ${styles.cloud3}`} src={cloud3} alt=""/>
        <img className={`${styles.hand} ${styles.leftHand}`} src={leftHand} alt=""/>
        <img className={`${styles.hand} ${styles.rightHand}`} src={rightHand} alt=""/>
        <img className={styles.legs} src={legs} alt=""/>
        <LogoLiga className={styles.logoLiga} />
        <picture>
          <source type="image/avif" srcSet={`${raceLigaAvif1x} 1x, ${raceLigaAvif2x} 2x`}/>
          <source type="image/webp" srcSet={`${raceLigaWebp1x} 1x, ${raceLigaWebp2x} 2x`}/>
          <img
            className={styles.logoRace}
            width="422"
            height="214"
            src={raceLigaPng1x}
            srcSet={`${raceLigaPng2x} 2x`}
            alt="Логотип Гонки Героев"/>
        </picture>
        <div className={styles.cloud1} />
      </section>

        <div className={styles.bottomControls}>
          <div className={styles.citySelectWrapper}>
            <CitySelect
              className="citySelect"
              classNamePrefix="citySelect"
              placeholder="Выберите город"
            />
          </div>
          <a className={styles.anchor} href="#citychoice">Принять участие</a>
        </div>
    </div>
  );
}

export default TopPage;
