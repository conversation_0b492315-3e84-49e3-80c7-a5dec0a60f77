import styles from './CityChoice.module.scss';
import Formats from '../formats/Formats';
import CitySelect from '../citySelect/CitySelect';
import { useEffect, useState, useCallback } from 'react';
import api from '../../api';
import { useDispatch, useSelector } from 'react-redux';
import { getEventsList, getOpenPageStatus, getSelectedCity } from '../../store/app/selectors';
import { setSelectedCity } from '../../store/action';
import { fetchEventsList } from '../../store/api-actions';
import { HERO_API } from '../../api';

function CityChoice({cityIdScreen}) {
  const dispatch = useDispatch();
  const [formats, setFormats] = useState([]);
  const [registrationClosed, setRegistrationClosed] = useState(false);

  const events = useSelector(getEventsList);
  const selectedCity = useSelector(getSelectedCity);
  const isFirstOpenPage = useSelector(getOpenPageStatus);

  const registrationCloseFilter = (event_city) => {
    const registrationCloseTime = new Date(event_city.registration_close * 1000);
    const currentTime = new Date();
    if (registrationCloseTime < currentTime) {
      return true
    } else {
      return false
    }
  };

  const fetchFormats = useCallback(async (publicId) => {
    const eventCityPublicId = publicId || selectedCity?.public_id;
    if (!eventCityPublicId || !events) return;

    const filterCity = events.filter((el) => el.city.id === cityIdScreen); // поиск всех мероприятий в выбранном городе
    const currentCityIndex = filterCity.findIndex((el) => el.public_id === publicId);

    try {
      const response = await api.get(`/api/event_format/event/${eventCityPublicId}`);

      if (response.data) {
        if (response?.data?.values?.length > 0) {
          const formatTicketsLeft = response.data.values.find((el) => el?.tickets_left?.athlete > 0);

          if (isFirstOpenPage && !formatTicketsLeft && currentCityIndex < (filterCity.length - 1)) {
            const nextCity = filterCity[currentCityIndex + 1];
            dispatch(setSelectedCity(nextCity));
          } else {
            setFormats(response.data.values)
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
  }, [selectedCity, events, cityIdScreen, isFirstOpenPage, dispatch]);

  useEffect(() => {
    !events && dispatch(fetchEventsList());
  }, [events, dispatch]);

  useEffect(() => {
    if (events && selectedCity) {
      setRegistrationClosed(registrationCloseFilter(selectedCity))
      fetchFormats(selectedCity.public_id);
    }
  }, [selectedCity, events, fetchFormats]);

  return (
    <section className={styles.cityChoice}>
      <div className={styles.cityChoiceBack}>
        <div className={styles.wrapper} id="citychoice">
          <h2 className={styles.title}><span className={styles.titleWrap}>В твоем городе</span></h2>
          <p className={styles.desc}>Выбери свой город</p>
          <div className={styles.selectWrap}>
            <CitySelect cityIdScreen={cityIdScreen} />
          </div>
          { registrationClosed && <section id="formats"><h2 className={styles.title2}>Продажа завершена</h2></section> }

          {selectedCity?.info?.guide && (
            <a className={styles.link} href={`${HERO_API}${selectedCity.info.guide}`}>Гайд участника</a>
          )}
        </div>

        
      </div>

      { (formats.length > 0 && !registrationClosed ) && <Formats formats={formats} city={selectedCity}/> }
    </section>
  );
}

export default CityChoice;
