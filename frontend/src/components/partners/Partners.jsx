import styles from './Partners.module.scss';
import Container from '../container/Container';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Navigation } from 'swiper';
import 'swiper/swiper.scss';
import 'swiper/components/navigation/navigation.scss';

// Установка модулей Swiper
SwiperCore.use([Navigation]);

function Partners() {
  const sliderParams = {
    slidesPerView: 4,
    spaceBetween: 30,
    navigation: {
      prevEl: `.${styles.sliderNavPrev}`,
      nextEl: `.${styles.sliderNavNext}`,
    },
  }

  return (
    <Container>
      <section className={styles.partners}>
        <h2 className={styles.title}>Партнеры</h2>

        <div className={styles.sliderWrap}>
          <Swiper
            {...sliderParams}
            className={styles.slider}
          >
            <SwiperSlide className={styles.slide}>
              <li className={styles.item} >
                {/*<img className={styles.img} src={}  alt="Партнёр"/>*/}
              </li>
            </SwiperSlide>
            <SwiperSlide className={styles.slide}>
              <li className={styles.item} >
                {/*<img className={styles.img} src={}  alt="Партнёр"/>*/}
              </li>
            </SwiperSlide>
            <SwiperSlide className={styles.slide}>
              <li className={styles.item} >
                {/*<img className={styles.img} src={}  alt="Партнёр"/>*/}
              </li>
            </SwiperSlide>
            <SwiperSlide className={styles.slide}>
              <li className={styles.item} >
                {/*<img className={styles.img} src={}  alt="Партнёр"/>*/}
              </li>
            </SwiperSlide>
          </Swiper>
          <div className={styles.sliderNavPrev} />
          <div className={styles.sliderNavNext} />
        </div>
      </section>
    </Container>
  );
}

export default Partners;
