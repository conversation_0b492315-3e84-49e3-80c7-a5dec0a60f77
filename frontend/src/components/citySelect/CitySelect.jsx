import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import Select from 'react-select';
import { getEventsList, getSelectedCity } from '../../store/app/selectors';
import { setOpenPageStatus, setSelectedCity } from '../../store/action';
import { fetchEventsList } from '../../store/api-actions';
import { setCitiesOptions } from '../../utils/common';
import { cityRoutes } from '../../const/routes';
import './CitySelect.scss';

function CitySelect({ 
  cityIdScreen, 
  className = "citySelect", 
  classNamePrefix = "citySelect",
  placeholder = "Выберите город",
  isSearchable = false,
  onCityChange
}) {
  const history = useHistory();
  const dispatch = useDispatch();
  const [cityOptions, setCityOptions] = useState([]);
  const [defaultCityOption, setDefaultCityOption] = useState({});

  const events = useSelector(getEventsList);
  const selectedCity = useSelector(getSelectedCity);

  useEffect(() => {
    !events && dispatch(fetchEventsList());

    if (!selectedCity && events) {
      const dateNow = Math.round(Date.now() / 1000);

      if (cityIdScreen) {
        const filterCity = events.filter((el) => el.city.id === cityIdScreen);
        const foundCity = filterCity.find((el) => el.city.id === cityIdScreen && el.registration_close > dateNow) ?? filterCity[filterCity?.length - 1];
        const cityOption = setCitiesOptions(events).find((el) => el.cityPublicId === foundCity.public_id);

        setDefaultCityOption(cityOption);
        dispatch(setSelectedCity(foundCity));
      } else {
        setDefaultCityOption(setCitiesOptions(events)[0]);
        dispatch(setSelectedCity(events[0]));
      }

      setCityOptions(setCitiesOptions(events));
    }
  }, [events, cityIdScreen, selectedCity, dispatch]);

  useEffect(() => {
    if (events && selectedCity) {
      const cityOption = setCitiesOptions(events).find((el) => el.value === selectedCity.public_id);

      setCityOptions(setCitiesOptions(events));
      setDefaultCityOption(cityOption);
    }
  }, [selectedCity, events]);

  const handleChangeCity = (evt) => {
    const city = cityRoutes.find((el) => el.cityId === evt.cityId);

    dispatch(setOpenPageStatus(false));

    if (city) {
      const foundCity = events.find((el) => el.public_id === evt.value);

      history.push(`/race/${city.cityName}`);
      dispatch(setSelectedCity(foundCity));
      
      // Вызываем колбэк если он передан
      if (onCityChange) {
        onCityChange(foundCity, evt);
      }
    }
  };

  if (cityOptions.length === 0) {
    return null;
  }

  return (
    <Select
      className={className}
      classNamePrefix={classNamePrefix}
      options={cityOptions}
      value={defaultCityOption}
      placeholder={placeholder}
      onChange={handleChangeCity}
      isSearchable={isSearchable}
    />
  );
}

export default CitySelect;
